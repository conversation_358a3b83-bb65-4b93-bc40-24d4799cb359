// Package csv provides low-level CSV file parsing functionality.
// It handles the raw parsing of CSV data without any knowledge of
// business objects or domain-specific logic.
//
// This package is part of the format layer in the system architecture:
// - Format layer (io/formats/) handles raw parsing
// - Loader layer (data/*/loader.go) provides domain-specific loading
// - Converter layer handles type conversion
// - Validation layer handles business rules
package csv

import (
	"encoding/csv"
	"fmt"
	"io"
	"os"

	"github.com/berrijam/mulberri/internal/utils/logger"
)

// CSVData holds the raw CSV data in an efficient structure
// to avoid unnecessary copying of data throughout the system.
// It represents the parsed CSV content with clear separation between
// headers and data records.
//
// The structure is designed for memory efficiency and provides
// quick access to dataset dimensions and content.
// This structure is used for both training and prediction datasets.
type CSVData struct {
	// Headers contains the column names from the first row.
	// This is always populated as the parser treats the first row as headers.
	Headers []string

	// Records contains all data rows as string slices.
	// Each row corresponds to one record from the CSV, excluding the header row.
	// All values are kept as strings for maximum flexibility in type conversion.
	Records [][]string

	// NumColumns is the number of columns in the dataset.
	// This value is determined from the first row and validated across all rows.
	NumColumns int

	// NumRows is the number of data rows (excluding header).
	// This represents the actual data records available for processing.
	NumRows int
}

// Release frees the memory used by the CSVData structure.
// This method should be called after converting CSVData to a typed Dataset
// to avoid double memory consumption. After calling Release, the CSVData
// should not be used anymore.
//
// Example:
//
//	csvData, err := parser.ParseCsvFile("data.csv")
//	if err != nil {
//	    return err
//	}
//
//	// Convert to typed dataset
//	dataset := ConvertToDataset(csvData, featureInfo)
//
//	// Free memory used by raw CSV data
//	csvData.Release()
//	csvData = nil
func (d *CSVData) Release() {
	// Clear slices to help GC
	d.Headers = nil
	d.Records = nil
	d.NumColumns = 0
	d.NumRows = 0
}

// Parser handles CSV file parsing with configurable options.
// It provides a flexible interface for parsing CSV data from various sources
// while maintaining consistent behavior and error handling.
//
// The parser is designed to be reusable and thread-safe for the parsing
// operations (though individual instances should not be used concurrently).
type Parser struct {
	// Delimiter is the field delimiter (default: comma).
	// Can be customized to handle different CSV dialects (e.g., semicolon-separated).
	Delimiter rune

	// HasHeader indicates if the first row contains column headers.
	// Note: Currently the parser always treats the first row as headers,
	// so this field is not actively used but kept for future compatibility.
	HasHeader bool

	// TrimLeadingSpace removes leading whitespace from fields.
	// This can be useful for cleaning up CSV data with inconsistent spacing.
	TrimLeadingSpace bool
}

// NewParser creates a new CSV parser with default settings.
// The parser is initialized with comma as the delimiter and other
// settings set to their default values.
//
// Returns:
//   - *Parser: A new parser instance ready for use
//
// Default settings:
//   - Delimiter: ',' (comma)
//   - HasHeader: false (not currently used)
//   - TrimLeadingSpace: false
//
// Example:
//
//	parser := NewParser()
//	parser.Delimiter = ';' // Customize if needed
//	data, err := parser.ParseCsvFile("data.csv")
func NewParser() *Parser {
	return &Parser{
		Delimiter: ',',
	}
}

// ParseCsvFile parses a CSV file and returns the structured data.
// This is a convenience method that opens the file and delegates
// to the Parse method for the actual parsing logic.
//
// Parameters:
//   - filepath: The path to the CSV file to parse
//
// Returns:
//   - *CSVData: The parsed CSV data structure
//   - error: An error if the file cannot be opened or parsed
//
// The method handles file opening and closing automatically.
// The first row is always treated as headers, and subsequent rows
// are treated as data records.
//
// Example:
//
//	parser := NewParser()
//	data, err := parser.ParseCsvFile("data.csv")
//	if err != nil {
//	    log.Fatal(err)
//	}
//	fmt.Printf("Headers: %v\n", data.Headers)
//
// Errors:
//   - File not found or permission errors
//   - CSV parsing errors from the underlying Parse method
func (p *Parser) ParseCsvFile(filepath string) *CSVData {
	file, err := os.Open(filepath)
	if err != nil {
		logger.Error(fmt.Sprintf("failed to open file %s: %w", filepath, err))
		return nil
	}
	defer file.Close()

	return p.Parse(file)
}

// Parse parses CSV data from an io.Reader and returns the structured data.
// This is the core parsing method that handles the actual CSV processing logic.
// It reads all CSV records at once and structures them into CSVData.
//
// Parameters:
//   - reader: An io.Reader containing CSV data to parse
//
// Returns:
//   - *CSVData: The structured CSV data with headers and records separated
//   - error: An error if parsing fails or data is invalid
//
// Behavior:
//   - The first row is always treated as headers
//   - Subsequent rows are treated as data records
//   - All rows must have the same number of columns (enforced by CSV reader)
//   - Empty CSV returns an error
//   - Uses the configured Delimiter for field separation
//   - Applies TrimLeadingSpace setting to remove leading whitespace
//   - Enforces strict quote handling (RFC 4180 compliant)
//
// The method validates that all data rows have the same number of columns
// as the header row and returns an error if inconsistencies are found.
//
// Example:
//
//	parser := NewParser()
//	data, err := parser.Parse(strings.NewReader("name,age\nJohn,25\nJane,30"))
//	if err != nil {
//	    log.Fatal(err)
//	}
//	fmt.Printf("Found %d records with %d columns\n", data.NumRows, data.NumColumns)
//
// Errors:
//   - CSV format errors (malformed quotes, etc.)
//   - Inconsistent column counts between rows
//   - Empty CSV data
func (p *Parser) Parse(reader io.Reader) *CSVData {
	csvReader := csv.NewReader(reader)

	// Configure the CSV reader
	csvReader.Comma = p.Delimiter
	csvReader.FieldsPerRecord = 0                   // Validate consistent field count (set from first record)
	csvReader.TrimLeadingSpace = p.TrimLeadingSpace // Use parser's TrimLeadingSpace setting
	csvReader.LazyQuotes = false                    // Enforce strict quotes (RFC 4180 compliant)

	// Read all records at once
	allRecords, err := csvReader.ReadAll()
	if err != nil {
		logger.Error(fmt.Sprintf("failed to parse CSV: %w", err))
		return nil
	}

	if len(allRecords) == 0 {
		logger.Warn("Empty CSV data provided")
		return &CSVData{
			Headers:    []string{},
			Records:    [][]string{},
			NumColumns: 0,
			NumRows:    0,
		}
	}

	var headers []string
	var records [][]string
	numColumns := len(allRecords[0])

	if len(allRecords) < 1 {
		logger.Warn("No data rows found in CSV")
		return nil
	}
	headers = allRecords[0]
	records = allRecords[1:]

	// Note: Column count validation is handled by csvReader.FieldsPerRecord = 0

	return &CSVData{
		Headers:    headers,
		Records:    records,
		NumColumns: numColumns,
		NumRows:    len(records),
	}
}
