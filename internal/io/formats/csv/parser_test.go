package csv

import (
	"os"
	"path/filepath"
	"strings"
	"testing"
)

func TestParseBasic(t *testing.T) {
	csvData := `name,age,city
John,25,NYC
Jane,30,LA
Bob,35,Chicago`

	parser := NewParser()
	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check headers (first row is always treated as headers)
	expectedHeaders := []string{"name", "age", "city"}
	if len(data.Headers) != len(expectedHeaders) {
		t.Fatalf("Expected %d headers, got %d", len(expectedHeaders), len(data.Headers))
	}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.<PERSON><PERSON><PERSON>("Header %d: expected %s, got %s", i, expected, data.Headers[i])
		}
	}

	// Check dimensions
	if data.NumColumns != 3 {
		t.<PERSON>("Expected 3 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 3 {
		t.<PERSON><PERSON><PERSON>("Expected 3 data rows, got %d", data.NumRows)
	}

	// Check data records
	expectedRecords := [][]string{
		{"John", "25", "NYC"},
		{"Jane", "30", "LA"},
		{"Bob", "35", "Chicago"},
	}
	if len(data.Records) != len(expectedRecords) {
		t.Fatalf("Expected %d records, got %d", len(expectedRecords), len(data.Records))
	}
	for i, expectedRow := range expectedRecords {
		if len(data.Records[i]) != len(expectedRow) {
			t.Errorf("Row %d: expected %d columns, got %d", i, len(expectedRow), len(data.Records[i]))
			continue
		}
		for j, expectedValue := range expectedRow {
			if data.Records[i][j] != expectedValue {
				t.Errorf("Row %d, Column %d: expected %s, got %s", i, j, expectedValue, data.Records[i][j])
			}
		}
	}
}

func TestParseSingleRow(t *testing.T) {
	csvData := `name,age,city`

	parser := NewParser()
	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check headers
	expectedHeaders := []string{"name", "age", "city"}
	if len(data.Headers) != len(expectedHeaders) {
		t.Fatalf("Expected %d headers, got %d", len(expectedHeaders), len(data.Headers))
	}

	// Check dimensions - no data rows since only header exists
	if data.NumColumns != 3 {
		t.Errorf("Expected 3 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 data rows, got %d", data.NumRows)
	}
	if len(data.Records) != 0 {
		t.Errorf("Expected 0 records, got %d", len(data.Records))
	}
}

func TestParseEmpty(t *testing.T) {
	csvData := ``

	parser := NewParser()
	data, err := parser.Parse(strings.NewReader(csvData))

	// Parser should return an error for empty CSV
	if err == nil {
		t.Fatal("Expected error for empty CSV, got nil")
	}
	if !strings.Contains(err.Error(), "no record found") {
		t.Errorf("Expected 'no record found' error, got: %v", err)
	}

	// Data should still be returned with zero values
	if data == nil {
		t.Fatal("Expected data to be returned even with error")
	}
	if data.NumColumns != 0 {
		t.Errorf("Expected 0 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 0 {
		t.Errorf("Expected 0 rows, got %d", data.NumRows)
	}
}

func TestParseInconsistentColumns(t *testing.T) {
	csvData := `name,age,city
John,25,NYC
Jane,30
Bob,35,Chicago,Extra`

	parser := NewParser()
	_, err := parser.Parse(strings.NewReader(csvData))

	// Should return an error for inconsistent column counts
	if err == nil {
		t.Fatal("Expected error for inconsistent column counts, got nil")
	}
	if !strings.Contains(err.Error(), "wrong number of fields") {
		t.Errorf("Expected 'wrong number of fields' error, got: %v", err)
	}
}

func TestParseCustomDelimiter(t *testing.T) {
	csvData := `name;age;city
John;25;NYC
Jane;30;LA`

	parser := NewParser()
	parser.Delimiter = ';'

	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check headers
	expectedHeaders := []string{"name", "age", "city"}
	if len(data.Headers) != len(expectedHeaders) {
		t.Fatalf("Expected %d headers, got %d", len(expectedHeaders), len(data.Headers))
	}

	// Check data
	if data.NumRows != 2 {
		t.Errorf("Expected 2 data rows, got %d", data.NumRows)
	}
	if data.Records[0][0] != "John" {
		t.Errorf("Expected first record first field to be 'John', got %s", data.Records[0][0])
	}
}

func TestParseCsvFile(t *testing.T) {
	// Create a temporary CSV file
	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	csvContent := `name,age,city
John,25,NYC
Jane,30,LA`

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test file: %v", err)
	}

	parser := NewParser()
	data, err := parser.ParseCsvFile(csvFile)
	if err != nil {
		t.Fatalf("ParseCsvFile failed: %v", err)
	}

	// Check basic structure
	if data.NumColumns != 3 {
		t.Errorf("Expected 3 columns, got %d", data.NumColumns)
	}
	if data.NumRows != 2 {
		t.Errorf("Expected 2 data rows, got %d", data.NumRows)
	}
	if len(data.Headers) != 3 {
		t.Errorf("Expected 3 headers, got %d", len(data.Headers))
	}
}

func TestParseCsvFileNotFound(t *testing.T) {
	parser := NewParser()
	_, err := parser.ParseCsvFile("nonexistent.csv")

	if err == nil {
		t.Fatal("Expected error for nonexistent file, got nil")
	}
	if !strings.Contains(err.Error(), "failed to open file") {
		t.Errorf("Expected file open error, got: %v", err)
	}
}

func TestParseTrimLeadingSpace(t *testing.T) {
	csvData := `name, age, city
 John , 25 , NYC
 Jane , 30 , LA `

	parser := NewParser()
	parser.TrimLeadingSpace = true

	data, err := parser.Parse(strings.NewReader(csvData))
	if err != nil {
		t.Fatalf("Parse failed: %v", err)
	}

	// Check that leading spaces are trimmed from headers
	expectedHeaders := []string{"name", "age", "city"}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.Errorf("Header %d: expected '%s', got '%s'", i, expected, data.Headers[i])
		}
	}

	// Check that leading spaces are trimmed from data (but trailing spaces remain)
	if data.Records[0][0] != "John " {
		t.Errorf("Expected first field to be 'John ', got '%s'", data.Records[0][0])
	}
	if data.Records[0][1] != "25 " {
		t.Errorf("Expected second field to be '25 ', got '%s'", data.Records[0][1])
	}
}

func TestParseStrictQuotes(t *testing.T) {
	// Test that LazyQuotes = false enforces strict quote handling
	csvData := `name,description
John,"He said "Hello" to me"
Jane,"Simple text"`

	parser := NewParser()
	_, err := parser.Parse(strings.NewReader(csvData))

	// Should return an error due to improper quote handling
	if err == nil {
		t.Fatal("Expected error for improper quotes, got nil")
	}
	if !strings.Contains(err.Error(), "quote") && !strings.Contains(err.Error(), "parse") {
		t.Errorf("Expected quote-related error, got: %v", err)
	}
}
