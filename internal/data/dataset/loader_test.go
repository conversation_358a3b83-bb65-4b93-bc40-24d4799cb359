package dataset

import (
	"os"
	"path/filepath"
	"testing"
)

func TestLoaderBasic(t *testing.T) {
	// Create a temporary CSV file
	csvContent := `name,age,salary,active
<PERSON>,25,50000.5,true
<PERSON>,30,75000.0,false
<PERSON>,35,60000.25,true`

	tmpDir := t.TempDir()
	csvFile := filepath.Join(tmpDir, "test.csv")

	err := os.WriteFile(csvFile, []byte(csvContent), 0644)
	if err != nil {
		t.Fatalf("Failed to create test CSV file: %v", err)
	}

	// Load CSV data
	loader := NewLoader()
	data := loader.LoadCSV(csvFile)
	if data == nil {
		t.Fatalf("LoadCSV failed: %v", err)
	}

	// Verify basic structure
	if data.NumRows != 3 {
		t.<PERSON><PERSON><PERSON>("Expected 3 rows, got %d", data.NumRows)
	}

	if data.NumColumns != 4 {
		t.<PERSON><PERSON><PERSON>("Expected 4 columns, got %d", data.NumColumns)
	}

	expectedHeaders := []string{"name", "age", "salary", "active"}
	for i, expected := range expectedHeaders {
		if data.Headers[i] != expected {
			t.Errorf("Header %d: expected %s, got %s", i, expected, data.Headers[i])
		}
	}
}
